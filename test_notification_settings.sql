-- Update notification_settings with sample admin data
UPDATE public.notification_settings 
SET 
  post_office_logo_url = 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/4a/India_Post_Logo.svg/200px-India_Post_Logo.svg.png',
  admin_logo_url = 'https://via.placeholder.com/100x50/4f46e5/ffffff?text=ADMIN',
  admin_name = 'Post Office Investment Agency',
  admin_email = '<EMAIL>',
  admin_phone = '+91-9876543210',
  admin_address = 'Main Post Office, Central District, City - 123456',
  admin_tagline = 'Trusted Investment Solutions Since 1854'
WHERE id = (SELECT id FROM public.notification_settings LIMIT 1);

-- If no record exists, insert one
INSERT INTO public.notification_settings (
  post_office_logo_url,
  admin_logo_url,
  admin_name,
  admin_email,
  admin_phone,
  admin_address,
  admin_tagline,
  alert_days_before,
  tds_percentage
)
SELECT 
  'https://upload.wikimedia.org/wikipedia/commons/thumb/4/4a/India_Post_Logo.svg/200px-India_Post_Logo.svg.png',
  'https://via.placeholder.com/100x50/4f46e5/ffffff?text=ADMIN',
  'Post Office Investment Agency',
  '<EMAIL>',
  '+91-9876543210',
  'Main Post Office, Central District, City - 123456',
  'Trusted Investment Solutions Since 1854',
  7,
  10.00
WHERE NOT EXISTS (SELECT 1 FROM public.notification_settings);
